@extends('layouts.authenticated')

@section('page.title', 'Duyệt giải trình chấm công - Manager')

@section('page.breadcrumb')
    <ol class="breadcrumb">
        <li><a href="{{ route('auth.home') }}"><i class="fa fa-dashboard"></i> Trang chủ</a></li>
        <li><a href="{{ route('attendance-explanation.index') }}">Giải trình chấm công</a></li>
        <li class="active"><PERSON><PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON> tr<PERSON><PERSON> (Manager)</li>
    </ol>
@endsection

@section('page.content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">
                        <i class="fa fa-check-circle"></i> <PERSON>yệt giải trình chấm công - Manager
                        <span class="badge badge-warning pull-right">{{ $stats['pending_count'] }} đơn chờ duyệt</span>
                    </h3>
                </div>
                <div class="panel-body">
                    <!-- Navigation Tabs -->
                    <ul class="nav nav-tabs" role="tablist" style="margin-bottom: 20px;">
                        <li role="presentation" class="active">
                            <a href="#pending-tab" aria-controls="pending-tab" role="tab" data-toggle="tab">
                                <i class="fa fa-clock-o text-warning"></i> Chờ duyệt ({{ $stats['pending_count'] }})
                            </a>
                        </li>
                        <li role="presentation">
                            <a href="#history-tab" aria-controls="history-tab" role="tab" data-toggle="tab">
                                <i class="fa fa-history text-info"></i> Lịch sử duyệt (1 tháng)
                            </a>
                        </li>
                        <li role="presentation">
                            <a href="#all-employees-tab" aria-controls="all-employees-tab" role="tab" data-toggle="tab">
                                <i class="fa fa-users text-success"></i> Tất cả nhân viên
                            </a>
                        </li>
                    </ul>

                    <!-- Chú thích trạng thái -->
                    <div class="panel panel-info" style="margin-bottom: 20px;">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <i class="fa fa-info-circle"></i> Chú thích trạng thái giải trình
                            </h4>
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="legend-item">
                                        <span class="legend-icon">🟡</span>
                                        <span class="label label-warning">Chờ Manager duyệt</span>
                                        <span class="legend-desc">Đang chờ bạn xem xét và quyết định</span>
                                    </div>
                                    <div class="legend-item">
                                        <span class="legend-icon">🟢</span>
                                        <span class="label label-success">Đã duyệt</span>
                                        <span class="legend-desc">Manager đã phê duyệt, chuyển HR</span>
                                    </div>
                                    <div class="legend-item">
                                        <span class="legend-icon">🔴</span>
                                        <span class="label label-danger">Đã từ chối</span>
                                        <span class="legend-desc">Manager đã từ chối giải trình</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="legend-item">
                                        <span class="legend-icon">🔵</span>
                                        <span class="label label-info">Chờ HR</span>
                                        <span class="legend-desc">Đã duyệt, chờ HR xử lý tiếp</span>
                                    </div>
                                    <div class="legend-item">
                                        <span class="legend-icon">✅</span>
                                        <span class="label label-success">HR duyệt</span>
                                        <span class="legend-desc">Hoàn tất quy trình duyệt</span>
                                    </div>
                                    <div class="legend-item">
                                        <span class="legend-icon">❌</span>
                                        <span class="label label-danger">HR từ chối</span>
                                        <span class="legend-desc">HR đã từ chối sau khi Manager duyệt</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tab Content -->
                    <div class="tab-content">
                        <!-- Pending Tab -->
                        <div role="tabpanel" class="tab-pane active" id="pending-tab">
                            <!-- Bulk Actions Panel -->
                            <div id="bulkActionsPanel" class="bulk-actions-panel" style="display: none;">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="bulk-info">
                                            <i class="fa fa-check-square-o"></i>
                                            <span id="selectedCount">0</span> giải trình đã chọn
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-info btn-sm btn-block" id="selectAllBtn">
                                            <i class="fa fa-check-square"></i> Chọn tất cả
                                        </button>
                                    </div>
                                    <div class="col-md-4 text-right">
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-success btn-sm" id="bulkApproveBtn">
                                                <i class="fa fa-check"></i> Duyệt tất cả
                                            </button>
                                            <button type="button" class="btn btn-danger btn-sm" id="bulkRejectBtn">
                                                <i class="fa fa-times"></i> Từ chối tất cả
                                            </button>
                                            <button type="button" class="btn btn-default btn-sm" id="clearSelectionBtn">
                                                <i class="fa fa-refresh"></i> Bỏ chọn
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Filters for Pending -->
                            <div class="row" style="margin-bottom: 20px;">
                                <div class="col-md-4">
                                    <label>Tìm theo tên nhân viên:</label>
                                    <input type="text" id="employeeFilter" class="form-control" placeholder="Nhập tên nhân viên...">
                                </div>
                                <div class="col-md-3">
                                    <label>Tháng:</label>
                                    <select id="monthFilter" class="form-control">
                                        <option value="">Tất cả tháng</option>
                                        @for($i = 1; $i <= 12; $i++)
                                            <option value="{{ $i }}" {{ $i == date('n') ? 'selected' : '' }}>
                                                Tháng {{ $i }}
                                            </option>
                                        @endfor
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label>Năm:</label>
                                    <select id="yearFilter" class="form-control">
                                        @for($year = date('Y'); $year >= date('Y') - 2; $year--)
                                            <option value="{{ $year }}" {{ $year == date('Y') ? 'selected' : '' }}>
                                                {{ $year }}
                                            </option>
                                        @endfor
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label>&nbsp;</label>
                                    <button type="button" id="applyFilter" class="btn btn-primary form-control">
                                        <i class="fa fa-search"></i> Lọc
                                    </button>
                                </div>
                            </div>

                    <!-- Results -->
                    <div id="explanationsList">
                    @if($groupedPending->count() > 0)
                        @foreach($groupedPending as $userId => $explanations)
                            @php $user = $explanations->first()->user; @endphp
                            <!-- Employee Header - Modern Card Design -->
                            <div class="employee-card" style="margin-bottom: 15px;">
                                <div class="employee-header" data-toggle="collapse" data-target="#employee-{{ $userId }}" aria-expanded="false">
                                    <div class="employee-info">
                                        <div class="employee-avatar">
                                            <div class="avatar-circle">
                                                {{ strtoupper(substr($user->name, 0, 2)) }}
                                            </div>
                                        </div>
                                        <div class="employee-details">
                                            <div class="employee-name">
                                                {{ $user->name }}
                                            </div>
                                            <div class="employee-meta">
                                                <span class="employee-account">
                                                    <i class="fa fa-user-circle"></i> {{ $user->account }}
                                                </span>
                                                @if($user->staffDepartment)
                                                    <span class="employee-department">
                                                        <i class="fa fa-building"></i> {{ $user->staffDepartment->name }}
                                                    </span>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                    <div class="employee-stats">
                                        <div class="pending-count">
                                            <span class="count-number">{{ $explanations->count() }}</span>
                                            <span class="count-label">đơn chờ duyệt</span>
                                        </div>
                                        <div class="expand-icon">
                                            <i class="fa fa-chevron-down"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Employee Explanations -->
                            <div id="employee-{{ $userId }}" class="panel-collapse collapse">
                                @foreach($explanations as $explanation)
                                    <div class="panel panel-info" style="margin-left: 20px; margin-bottom: 10px;">
                                        <div class="panel-heading">
                                            <div class="row">
                                                <div class="col-md-1">
                                                    <div class="checkbox-wrapper">
                                                        <input type="checkbox" class="explanation-checkbox"
                                                               data-explanation-id="{{ $explanation->id }}"
                                                               data-user-name="{{ $user->name }}"
                                                               id="explanation_{{ $explanation->id }}">
                                                        <label for="explanation_{{ $explanation->id }}" class="checkbox-label">
                                                            <i class="fa fa-square-o unchecked"></i>
                                                            <i class="fa fa-check-square checked"></i>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-7">
                                                    <h5 class="panel-title">
                                                        <i class="fa fa-calendar"></i> {{ $explanation->date->format('d/m/Y') }}
                                                        <small class="text-muted">- {{ $explanation->explanation_type_text }}</small>
                                                    </h5>
                                                    <small class="text-muted">
                                                        <i class="fa fa-clock-o"></i> Tạo lúc: {{ $explanation->created_at->format('d/m/Y H:i') }}
                                                    </small>
                                                </div>
                                                <div class="col-md-4 text-right">
                                                    <span class="label label-warning">Chờ Manager duyệt</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="panel-body">
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <h6><strong>Nội dung giải trình:</strong></h6>
                                                    <p class="well well-sm">{{ $explanation->explanation }}</p>

                                                    @if($explanation->explanation_type === 'overtime' && $explanation->ot_hours)
                                                        <h6><strong>Số giờ OT:</strong></h6>
                                                        <p><span class="label label-info">{{ $explanation->ot_hours }} giờ</span></p>
                                                    @endif

                                                    @if($explanation->tagged_users && count($explanation->tagged_users) > 0)
                                                        <h6><strong>Đã tag:</strong></h6>
                                                        <p class="text-muted">{{ count($explanation->tagged_users) }} người được thông báo</p>
                                                    @endif
                                                </div>
                                                <div class="col-md-4">
                                                    <h6><strong>Quy trình duyệt:</strong></h6>
                                                    <div class="list-group">
                                                        <div class="list-group-item">
                                                            <i class="fa fa-user text-warning"></i>
                                                            <strong>Bước 1: Manager</strong>
                                                            <br><small class="text-warning">Đang chờ bạn duyệt</small>
                                                        </div>
                                                        <div class="list-group-item">
                                                            <i class="fa fa-building text-muted"></i>
                                                            <strong>Bước 2: HCNS</strong>
                                                            <br><small class="text-muted">Chờ manager duyệt trước</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <hr>

                                            <div class="row">
                                                <div class="col-md-12">
                                                    <h6><strong>Quyết định của Manager:</strong></h6>
                                                    <form class="manager-approval-form" data-explanation-id="{{ $explanation->id }}">
                                                        <div class="row">
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label>Quyết định:</label>
                                                                    <select name="status" class="form-control" required>
                                                                        <option value="">-- Chọn quyết định --</option>
                                                                        <option value="approved">✓ Duyệt</option>
                                                                        <option value="rejected">✗ Từ chối</option>
                                                                    </select>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label>Ghi chú (tùy chọn):</label>
                                                                    <textarea name="note" class="form-control" rows="2" placeholder="Ghi chú của manager..."></textarea>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="form-group">
                                                            <button type="submit" class="btn btn-primary">
                                                                <i class="fa fa-check"></i> Xác nhận quyết định
                                                            </button>

                                                            <button type="button" class="btn btn-default" onclick="window.location.reload()">
                                                                <i class="fa fa-refresh"></i> Làm mới
                                                            </button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @endforeach
                    @else
                        <div class="alert alert-info text-center">
                            <i class="fa fa-info-circle fa-2x"></i>
                            <h4>Không có giải trình nào cần duyệt</h4>
                            <p>Hiện tại không có đơn giải trình chấm công nào cần bạn duyệt.</p>
                        </div>
                            @endif
                            </div> <!-- End explanationsList -->
                        </div>

                        <!-- History Tab -->
                        <div role="tabpanel" class="tab-pane" id="history-tab">
                            <!-- Filters for History -->
                            <div class="filter-section">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label><i class="fa fa-search"></i> Tìm theo tên nhân viên:</label>
                                            <input type="text" id="historyEmployeeFilter" class="form-control" placeholder="Nhập tên nhân viên...">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label><i class="fa fa-filter"></i> Trạng thái:</label>
                                            <select id="historyStatusFilter" class="form-control">
                                                <option value="">Tất cả</option>
                                                <option value="approved">Đã duyệt</option>
                                                <option value="rejected">Đã từ chối</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label><i class="fa fa-calendar"></i> Tháng duyệt:</label>
                                            <select id="historyMonthFilter" class="form-control">
                                                <option value="">Tất cả tháng</option>
                                                @for($i = 1; $i <= 12; $i++)
                                                    <option value="{{ $i }}" {{ $i == date('n') ? 'selected' : '' }}>
                                                        Tháng {{ $i }}
                                                    </option>
                                                @endfor
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label>&nbsp;</label>
                                            <div class="btn-group" style="width: 100%;">
                                                <button type="button" id="applyHistoryFilter" class="btn btn-info modern-btn" style="width: 60%;">
                                                    <i class="fa fa-search"></i> Lọc
                                                </button>
                                                <button type="button" id="resetHistoryFilter" class="btn btn-default modern-btn" style="width: 40%;">
                                                    <i class="fa fa-refresh"></i> Reset
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- History Results -->
                            <div id="historyList">
                            @if($groupedHistory->count() > 0)
                                @foreach($groupedHistory as $userId => $explanations)
                                    @php
                                        $user = $explanations->first()->user;
                                        $approvedCount = $explanations->where('manager_status', 'approved')->count();
                                        $rejectedCount = $explanations->where('manager_status', 'rejected')->count();
                                    @endphp
                                    <!-- Employee History Header -->
                                    <div class="employee-card history-employee-group" style="margin-bottom: 15px;" data-user-name="{{ strtolower($user->name) }}" data-user-account="{{ strtolower($user->account) }}">
                                        <div class="employee-header history-header" data-toggle="collapse" data-target="#history-{{ $userId }}" aria-expanded="false">
                                            <div class="employee-info">
                                                <div class="employee-avatar">
                                                    <div class="avatar-circle history-avatar">
                                                        {{ strtoupper(substr($user->name, 0, 2)) }}
                                                    </div>
                                                </div>
                                                <div class="employee-details">
                                                    <div class="employee-name">
                                                        {{ $user->name }}
                                                    </div>
                                                    <div class="employee-meta">
                                                        <span class="employee-account">
                                                            <i class="fa fa-user-circle"></i> {{ $user->account }}
                                                        </span>
                                                        @if($user->staffDepartment)
                                                            <span class="employee-department">
                                                                <i class="fa fa-building"></i> {{ $user->staffDepartment->name }}
                                                            </span>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="employee-stats">
                                                <div class="history-stats">
                                                    @if($approvedCount > 0)
                                                        <span class="stat-item approved">
                                                            <span class="stat-number">{{ $approvedCount }}</span>
                                                            <span class="stat-label">duyệt</span>
                                                        </span>
                                                    @endif
                                                    @if($rejectedCount > 0)
                                                        <span class="stat-item rejected">
                                                            <span class="stat-number">{{ $rejectedCount }}</span>
                                                            <span class="stat-label">từ chối</span>
                                                        </span>
                                                    @endif
                                                </div>
                                                <div class="expand-icon">
                                                    <i class="fa fa-chevron-down"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Employee History Details -->
                                    <div id="history-{{ $userId }}" class="panel-collapse collapse">
                                        @foreach($explanations->sortByDesc('manager_approved_at') as $explanation)
                                            <div class="panel {{ $explanation->manager_status === 'approved' ? 'panel-success' : 'panel-danger' }} history-item" style="margin-left: 20px; margin-bottom: 10px;" data-status="{{ $explanation->manager_status }}">
                                                <div class="panel-heading">
                                                    <div class="row">
                                                        <div class="col-md-8">
                                                            <h6>
                                                                <i class="fa fa-calendar"></i> {{ $explanation->date->format('d/m/Y') }} - {{ $explanation->explanation_type_text }}
                                                                <span class="label label-{{ $explanation->manager_status === 'approved' ? 'success' : 'danger' }}">
                                                                    {{ $explanation->manager_status === 'approved' ? 'Đã duyệt' : 'Đã từ chối' }}
                                                                </span>
                                                            </h6>
                                                            <small class="text-muted">
                                                                <i class="fa fa-check"></i> Duyệt lúc: {{ $explanation->manager_approved_at->format('d/m/Y H:i') }}
                                                            </small>
                                                        </div>
                                                        <div class="col-md-4 text-right">
                                                            @if($explanation->hr_status === 'pending')
                                                                <span class="label label-warning">Chờ HR</span>
                                                            @elseif($explanation->hr_status === 'approved')
                                                                <span class="label label-success">HR duyệt</span>
                                                            @else
                                                                <span class="label label-danger">HR từ chối</span>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="panel-body">
                                                    <p><strong>Nội dung:</strong> {{ $explanation->explanation }}</p>
                                                    @if($explanation->explanation_type === 'overtime' && $explanation->ot_hours)
                                                        <p><strong>Số giờ OT:</strong> <span class="label label-info">{{ $explanation->ot_hours }} giờ</span></p>
                                                    @endif
                                                    @if($explanation->manager_note)
                                                        <p><strong>Ghi chú manager:</strong> {{ $explanation->manager_note }}</p>
                                                    @endif
                                                    <small class="text-muted">Tạo lúc: {{ $explanation->created_at->format('d/m/Y H:i') }}</small>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                @endforeach
                            @else
                                <div class="alert alert-info text-center">
                                    <i class="fa fa-info-circle fa-2x"></i>
                                    <h4>Chưa có lịch sử duyệt</h4>
                                    <p>Chưa có lịch sử duyệt giải trình nào trong 3 tháng gần đây.</p>
                                </div>
                            @endif
                            </div>
                        </div>

                        <!-- All Employees Tab -->
                        <div role="tabpanel" class="tab-pane" id="all-employees-tab">
                            <div class="filter-section">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label><i class="fa fa-search"></i> Tìm theo tên nhân viên:</label>
                                            <input type="text" id="allEmployeesFilter" class="form-control" placeholder="Nhập tên nhân viên...">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label><i class="fa fa-info-circle"></i> Hướng dẫn:</label>
                                            <div class="alert alert-info" style="margin: 0; padding: 8px 12px;">
                                                <small>
                                                    <i class="fa fa-users"></i> Danh sách tất cả nhân viên được quản lý và thống kê giải trình
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div id="allEmployeesList">
                                @if($allEmployees->count() > 0)
                                    @foreach($allEmployees as $employee)
                                        @php
                                            $stats = $employee->stats;
                                            $lastExplanationText = '';

                                            if ($stats->last_explanation_date) {
                                                $lastExplanationText = 'Giải trình gần nhất: ' . \Carbon\Carbon::parse($stats->last_explanation_date)->format('d/m/Y');
                                            } else {
                                                $lastExplanationText = 'Chưa có giải trình nào';
                                            }
                                        @endphp

                                        <!-- Modern Employee Card Design -->
                                        <div class="employee-card all-employee-item" style="margin-bottom: 15px;" data-employee-name="{{ strtolower($employee->name) }}" data-employee-account="{{ strtolower($employee->account) }}">
                                            <div class="employee-header all-employee-header">
                                                <div class="employee-info">
                                                    <div class="employee-avatar">
                                                        <div class="avatar-circle all-employee-avatar">
                                                            {{ strtoupper(substr($employee->name, 0, 2)) }}
                                                        </div>
                                                    </div>
                                                    <div class="employee-details">
                                                        <div class="employee-name">
                                                            {{ $employee->name }}
                                                        </div>
                                                        <div class="employee-meta">
                                                            <span class="employee-account">
                                                                <i class="fa fa-user-circle"></i> {{ $employee->account }}
                                                            </span>
                                                            @if($employee->department_name)
                                                                <span class="employee-department">
                                                                    <i class="fa fa-building"></i> {{ $employee->department_name }}
                                                                </span>
                                                            @endif
                                                        </div>
                                                        <div class="employee-last-activity">
                                                            <small class="text-muted">{{ $lastExplanationText }}</small>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="employee-stats">
                                                    <div class="all-employee-stats">
                                                        @if($stats->pending_count > 0)
                                                            <span class="stat-badge pending">
                                                                <span class="stat-number">{{ $stats->pending_count }}</span>
                                                                <span class="stat-label">chờ duyệt</span>
                                                            </span>
                                                        @endif
                                                        @if($stats->approved_count > 0)
                                                            <span class="stat-badge approved">
                                                                <span class="stat-number">{{ $stats->approved_count }}</span>
                                                                <span class="stat-label">đã duyệt</span>
                                                            </span>
                                                        @endif
                                                        @if($stats->rejected_count > 0)
                                                            <span class="stat-badge rejected">
                                                                <span class="stat-number">{{ $stats->rejected_count }}</span>
                                                                <span class="stat-label">từ chối</span>
                                                            </span>
                                                        @endif
                                                        @if($stats->total_count == 0)
                                                            <span class="stat-badge no-data">
                                                                <span class="stat-number">0</span>
                                                                <span class="stat-label">giải trình</span>
                                                            </span>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    <div class="alert alert-info text-center">
                                        <i class="fa fa-info-circle fa-2x"></i>
                                        <h4>Không có nhân viên nào</h4>
                                        <p>Bạn chưa được phân công quản lý nhân viên nào.</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


@endsection



@section('page.scripts')
<script>
$(document).ready(function() {
    // Function để hiển thị toast notification
    function showToast(type, title, message) {
        var toastClass = type === 'success' ? 'alert-success' : 'alert-danger';
        var iconClass = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';

        var toastHtml = '<div class="alert ' + toastClass + ' alert-dismissible" style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">' +
            '<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>' +
            '<h5><i class="fa ' + iconClass + '"></i> ' + title + '</h5>' +
            message +
        '</div>';

        $('body').append(toastHtml);

        // Tự động ẩn sau 5 giây
        setTimeout(function() {
            $('.alert').fadeOut(500, function() {
                $(this).remove();
            });
        }, 5000);
    }

    // Filter functionality for pending tab
    $('#applyFilter, #employeeFilter').on('click keyup', function(e) {
        if (e.type === 'keyup' && e.keyCode !== 13) return; // Only trigger on Enter key

        var employeeName = $('#employeeFilter').val().toLowerCase();
        var month = $('#monthFilter').val();
        var year = $('#yearFilter').val();

        $('.panel-primary').each(function() {
            var $panel = $(this);
            var employeeText = $panel.find('.panel-heading h5').text().toLowerCase();
            var showPanel = true;

            // Filter by employee name
            if (employeeName && !employeeText.includes(employeeName)) {
                showPanel = false;
            }

            // Filter by month/year (check all explanations for this employee)
            if ((month || year) && showPanel) {
                var $employeeGroup = $panel.next('.panel-collapse');
                var hasMatchingDate = false;

                $employeeGroup.find('.panel-info').each(function() {
                    var dateText = $(this).find('.panel-title').text();
                    var dateMatch = dateText.match(/(\d{2})\/(\d{2})\/(\d{4})/);
                    if (dateMatch) {
                        var itemMonth = dateMatch[2];
                        var itemYear = dateMatch[3];

                        var monthMatch = !month || itemMonth == month.padStart(2, '0');
                        var yearMatch = !year || itemYear == year;

                        if (monthMatch && yearMatch) {
                            hasMatchingDate = true;
                        }
                    }
                });

                if (!hasMatchingDate) {
                    showPanel = false;
                }
            }

            if (showPanel) {
                $panel.show();
                $panel.next('.panel-collapse').show();
            } else {
                $panel.hide();
                $panel.next('.panel-collapse').hide();
            }
        });

        // Update count
        var visibleCount = $('.panel-primary:visible').length;
        $('.badge').text(visibleCount + ' nhóm hiển thị');
    });

    // Filter functionality for history tab
    $('#applyHistoryFilter, #historyEmployeeFilter, #historyStatusFilter, #historyMonthFilter').on('click keyup change', function(e) {
        if (e.type === 'keyup' && e.keyCode !== 13) return;

        var employeeName = $('#historyEmployeeFilter').val().toLowerCase();
        var status = $('#historyStatusFilter').val();
        var month = $('#historyMonthFilter').val();

        $('.history-employee-group').each(function() {
            var $group = $(this);
            var employeeText = $group.find('.panel-heading h5').text().toLowerCase();
            var showGroup = false; // Thay đổi: mặc định ẩn group
            var hasVisibleItems = false;

            // Filter by employee name
            var nameMatch = !employeeName || employeeText.includes(employeeName);

            if (nameMatch) {
                // Filter individual history items first
                $group.next('.panel-collapse').find('.history-item').each(function() {
                    var $item = $(this);
                    var showItem = true;

                    // Filter by status
                    if (status && $item.data('status') !== status) {
                        showItem = false;
                    }

                    // Filter by month
                    if (month && showItem) {
                        var dateText = $item.find('h6').text();
                        var dateMatch = dateText.match(/(\d{2})\/(\d{2})\/(\d{4})/);
                        if (dateMatch) {
                            var itemMonth = dateMatch[2];
                            if (itemMonth != month.padStart(2, '0')) {
                                showItem = false;
                            }
                        }
                    }

                    if (showItem) {
                        $item.show();
                        hasVisibleItems = true; // Có ít nhất 1 item hiển thị
                    } else {
                        $item.hide();
                    }
                });

                // Chỉ hiển thị group nếu có items hiển thị
                showGroup = hasVisibleItems;
            }

            if (showGroup) {
                $group.show();
                $group.next('.panel-collapse').show();
            } else {
                $group.hide();
                $group.next('.panel-collapse').hide();
            }
        });

        // Cập nhật số lượng hiển thị
        var visibleGroups = $('.history-employee-group:visible').length;
        $('#historyList').find('.badge').text(visibleGroups + ' nhóm hiển thị');
    });

    // Reset History Filter
    $('#resetHistoryFilter').on('click', function() {
        // Reset form values
        $('#historyEmployeeFilter').val('');
        $('#historyStatusFilter').val('');
        $('#historyMonthFilter').val('');

        // Show all groups and items
        $('.history-employee-group').show();
        $('.history-employee-group').next('.panel-collapse').show();
        $('.history-item').show();

        // Update count
        var totalGroups = $('.history-employee-group').length;
        $('#historyList').find('.badge').text(totalGroups + ' nhóm hiển thị');
    });

    // Filter functionality for all employees tab
    $('#allEmployeesFilter').on('keyup', function(e) {
        var employeeName = $(this).val().toLowerCase();

        $('.all-employee-item').each(function() {
            var $item = $(this);
            var itemName = $item.data('employee-name') || '';
            var itemAccount = $item.data('employee-account') || '';

            var showItem = !employeeName ||
                          itemName.includes(employeeName) ||
                          itemAccount.includes(employeeName);

            if (showItem) {
                $item.show();
            } else {
                $item.hide();
            }
        });

        // Update count
        var visibleCount = $('.all-employee-item:visible').length;
        var totalCount = $('.all-employee-item').length;

        // Add or update count display
        if ($('#allEmployeesCount').length === 0) {
            $('#allEmployeesList').prepend('<div id="allEmployeesCount" class="alert alert-success" style="margin-bottom: 15px; padding: 8px 15px;"></div>');
        }
        $('#allEmployeesCount').html('<i class="fa fa-users"></i> Hiển thị <strong>' + visibleCount + '</strong> / <strong>' + totalCount + '</strong> nhân viên');
    });

    // Filter functionality for all employees tab
    $('#allEmployeesFilter').on('keyup', function() {
        var searchTerm = $(this).val().toLowerCase();

        $('.all-employee-item').each(function() {
            var $item = $(this);
            var employeeText = $item.find('h5').text().toLowerCase();

            if (employeeText.includes(searchTerm)) {
                $item.show();
            } else {
                $item.hide();
            }
        });
    });



    // Đảm bảo modal có thể đóng được
    $(document).on('click', '[data-dismiss="modal"]', function() {
        $(this).closest('.modal').modal('hide');
    });

    // Đóng modal khi click outside
    $(document).on('click', '.modal-backdrop', function() {
        $('.modal').modal('hide');
    });

    // Đóng modal khi nhấn ESC
    $(document).on('keydown', function(e) {
        if (e.keyCode === 27) { // ESC key
            $('.modal').modal('hide');
        }
    });

    // Xử lý form duyệt manager
    $(document).on('submit', '.manager-approval-form', function(e) {
        e.preventDefault();

        var form = $(this);
        var explanationId = form.data('explanation-id');
        var status = form.find('select[name="status"]').val();
        var note = form.find('textarea[name="note"]').val();

        if (!status) {
            showToast('error', 'Lỗi!', 'Vui lòng chọn quyết định!');
            return;
        }

        var confirmMessage = status === 'approved' ?
            'Bạn có chắc chắn muốn DUYỆT giải trình này?' :
            'Bạn có chắc chắn muốn TỪ CHỐI giải trình này?';

        if (!confirm(confirmMessage)) {
            return;
        }

        // Disable form
        form.find('button[type="submit"]').prop('disabled', true);
        form.find('button[type="submit"]').html('<i class="fa fa-spinner fa-spin"></i> Đang xử lý...');

        $.ajax({
            url: '/attendance-explanation/' + explanationId + '/manager-approve',
            method: 'POST',
            data: {
                status: status,
                note: note,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    showToast('success', 'Thành công!', response.message);
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                } else {
                    showToast('error', 'Lỗi!', response.message);
                    form.find('button[type="submit"]').prop('disabled', false);
                    form.find('button[type="submit"]').html('<i class="fa fa-check"></i> Xác nhận quyết định');
                }
            },
            error: function(xhr) {
                var message = 'Có lỗi xảy ra!';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                showToast('error', 'Lỗi!', message);
                form.find('button[type="submit"]').prop('disabled', false);
                form.find('button[type="submit"]').html('<i class="fa fa-check"></i> Xác nhận quyết định');
            }
        });
    });

    // Bulk Actions Functionality
    var selectedExplanations = [];

    // Checkbox change event
    $(document).on('change', '.explanation-checkbox', function() {
        var explanationId = $(this).data('explanation-id');
        var userName = $(this).data('user-name');
        var $panel = $(this).closest('.panel');

        if ($(this).is(':checked')) {
            // Add to selected list
            selectedExplanations.push({
                id: explanationId,
                userName: userName,
                element: $panel
            });
            $panel.addClass('explanation-selected');
        } else {
            // Remove from selected list
            selectedExplanations = selectedExplanations.filter(item => item.id !== explanationId);
            $panel.removeClass('explanation-selected');
        }

        updateBulkActionsPanel();
    });

    // Update bulk actions panel
    function updateBulkActionsPanel() {
        var count = selectedExplanations.length;
        var $visibleCheckboxes = $('.explanation-checkbox:visible');
        var totalVisible = $visibleCheckboxes.length;

        if (count > 0) {
            $('#selectedCount').text(count);
            $('#bulkActionsPanel').slideDown();
        } else {
            $('#bulkActionsPanel').slideUp();
        }

        // Update select all button text
        if (count === totalVisible && totalVisible > 0) {
            $('#selectAllBtn').html('<i class="fa fa-minus-square"></i> Bỏ chọn tất cả');
        } else {
            $('#selectAllBtn').html('<i class="fa fa-check-square"></i> Chọn tất cả');
        }
    }

    // Bulk approve button
    $('#bulkApproveBtn').on('click', function() {
        if (selectedExplanations.length === 0) {
            showToast('error', 'Lỗi!', 'Vui lòng chọn ít nhất một giải trình để duyệt');
            return;
        }

        showBulkActionModal('approved', 'DUYỆT');
    });

    // Bulk reject button
    $('#bulkRejectBtn').on('click', function() {
        if (selectedExplanations.length === 0) {
            showToast('error', 'Lỗi!', 'Vui lòng chọn ít nhất một giải trình để từ chối');
            return;
        }

        showBulkActionModal('rejected', 'TỪ CHỐI');
    });

    // Select all button
    $('#selectAllBtn').on('click', function() {
        var $visibleCheckboxes = $('.explanation-checkbox:visible');
        var allChecked = $visibleCheckboxes.length > 0 && $visibleCheckboxes.filter(':checked').length === $visibleCheckboxes.length;

        if (allChecked) {
            // Nếu tất cả đã được chọn, bỏ chọn tất cả
            $visibleCheckboxes.prop('checked', false).trigger('change');
            $(this).html('<i class="fa fa-check-square"></i> Chọn tất cả');
        } else {
            // Chọn tất cả
            $visibleCheckboxes.prop('checked', true).trigger('change');
            $(this).html('<i class="fa fa-minus-square"></i> Bỏ chọn tất cả');
        }
    });

    // Clear selection button
    $('#clearSelectionBtn').on('click', function() {
        $('.explanation-checkbox').prop('checked', false);
        $('.panel').removeClass('explanation-selected');
        selectedExplanations = [];
        updateBulkActionsPanel();
        $('#selectAllBtn').html('<i class="fa fa-check-square"></i> Chọn tất cả');
    });

    // Show bulk action modal
    function showBulkActionModal(action, actionText) {
        $('#bulkActionType').text(actionText);
        $('#bulkActionCount').text(selectedExplanations.length);
        $('#bulkNote').val('');

        // Build list of selected explanations
        var listHtml = '';
        selectedExplanations.forEach(function(item) {
            var $panel = item.element;
            var date = $panel.find('.panel-title').text().trim();

            listHtml += '<div class="list-group-item">';
            listHtml += '<h6 class="list-group-item-heading">';
            listHtml += '<i class="fa fa-user"></i> ' + item.userName;
            listHtml += '</h6>';
            listHtml += '<p class="list-group-item-text">' + date + '</p>';
            listHtml += '</div>';
        });

        $('#bulkActionList').html(listHtml);
        $('#confirmBulkAction').data('action', action);
        $('#bulkActionModal').modal('show');
    }

    // Confirm bulk action
    $('#confirmBulkAction').on('click', function() {
        var action = $(this).data('action');
        var note = $('#bulkNote').val();
        var explanationIds = selectedExplanations.map(item => item.id);

        if (explanationIds.length === 0) {
            showToast('error', 'Lỗi!', 'Không có giải trình nào được chọn');
            return;
        }

        // Disable button and show loading
        $(this).prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Đang xử lý...');

        // Send AJAX request
        $.ajax({
            url: '/attendance-explanation/bulk-manager-approve',
            method: 'POST',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content'),
                explanation_ids: explanationIds,
                status: action,
                note: note
            },
            success: function(response) {
                $('#bulkActionModal').modal('hide');

                if (response.success) {
                    showToast('success', 'Thành công!', response.message);

                    // Remove processed explanations from UI
                    selectedExplanations.forEach(function(item) {
                        item.element.fadeOut(500, function() {
                            $(this).remove();
                        });
                    });

                    // Clear selection
                    selectedExplanations = [];
                    updateBulkActionsPanel();

                    // Update counts
                    setTimeout(function() {
                        window.location.reload();
                    }, 2000);
                } else {
                    showToast('error', 'Lỗi!', response.message || 'Có lỗi xảy ra khi xử lý');
                }
            },
            error: function(xhr) {
                var message = 'Có lỗi xảy ra khi xử lý yêu cầu';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    var errors = xhr.responseJSON.errors;
                    message = Object.values(errors).flat().join('<br>');
                }
                showToast('error', 'Lỗi!', message);
            },
            complete: function() {
                $('#confirmBulkAction').prop('disabled', false).html('<i class="fa fa-check"></i> Xác nhận thực hiện');
                $('#bulkActionModal').modal('hide');
            }
        });
    });
});

// Function to get explanation type text
function getExplanationTypeText(type) {
    var types = {
        'late': 'Đi muộn',
        'early': 'Về sớm',
        'insufficient_hours': 'Thiếu giờ làm việc',
        'no_checkin': 'Quên checkin',
        'no_checkout': 'Quên checkout',
        'overtime': 'Làm thêm giờ (OT)',
        'other': 'Khác'
    };
    return types[type] || 'Không xác định';
}


</script>

<style>
/* Employee Card Styles */
.employee-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid #e8e8e8;
    transition: all 0.3s ease;
    overflow: hidden;
}

.employee-card:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.employee-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    cursor: pointer;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-bottom: 1px solid #e8e8e8;
}

.employee-header:hover {
    background: linear-gradient(135deg, #e3f2fd 0%, #f8f9fa 100%);
}

.employee-info {
    display: flex;
    align-items: center;
    flex: 1;
}

.employee-avatar {
    margin-right: 16px;
}

.avatar-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #2196F3, #1976D2);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 16px;
    text-transform: uppercase;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
}

.history-avatar {
    background: linear-gradient(135deg, #17a2b8, #138496) !important;
    box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3) !important;
}

.employee-details {
    flex: 1;
}

.employee-name {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
}

.employee-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    color: #7f8c8d;
    font-size: 14px;
}

.employee-account,
.employee-department {
    display: flex;
    align-items: center;
    gap: 6px;
}

.employee-account i,
.employee-department i {
    color: #95a5a6;
}

.employee-stats {
    display: flex;
    align-items: center;
    gap: 16px;
}

.pending-count {
    text-align: center;
    padding: 8px 16px;
    background: linear-gradient(135deg, #ff9800, #f57c00);
    color: white;
    border-radius: 20px;
    box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
}

.count-number {
    display: block;
    font-size: 20px;
    font-weight: bold;
    line-height: 1;
}

.count-label {
    display: block;
    font-size: 12px;
    opacity: 0.9;
}

.history-stats {
    display: flex;
    gap: 12px;
}

.stat-item {
    text-align: center;
    padding: 6px 12px;
    border-radius: 16px;
    color: white;
    font-size: 12px;
    font-weight: 500;
}

.stat-item.approved {
    background: linear-gradient(135deg, #4caf50, #388e3c);
    box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
}

.stat-item.rejected {
    background: linear-gradient(135deg, #f44336, #d32f2f);
    box-shadow: 0 2px 6px rgba(244, 67, 54, 0.3);
}

.stat-number {
    display: block;
    font-size: 16px;
    font-weight: bold;
    line-height: 1;
}

.stat-label {
    display: block;
    font-size: 10px;
    opacity: 0.9;
}

.expand-icon {
    color: #95a5a6;
    font-size: 16px;
    transition: transform 0.3s ease;
}

.employee-header[aria-expanded="true"] .expand-icon {
    transform: rotate(180deg);
}

/* Legend styles */
.legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    padding: 5px 0;
}

.legend-icon {
    font-size: 16px;
    margin-right: 8px;
    width: 20px;
    text-align: center;
}

.legend-item .label {
    margin-right: 10px;
    min-width: 120px;
    text-align: center;
}

.legend-desc {
    color: #666;
    font-size: 13px;
    font-style: italic;
}

/* All Employees Tab Styles */
.all-employee-header {
    background: linear-gradient(135deg, #f3e5f5 0%, #f8f9fa 100%);
}

.all-employee-header:hover {
    background: linear-gradient(135deg, #e1bee7 0%, #f3e5f5 100%);
}

.all-employee-avatar {
    background: linear-gradient(135deg, #9c27b0, #7b1fa2);
    box-shadow: 0 2px 8px rgba(156, 39, 176, 0.3);
}

.employee-last-activity {
    margin-top: 8px;
    font-size: 13px;
    color: #6c757d;
}

.all-employee-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 8px;
}

.stat-badge {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    padding: 4px 8px;
    border-radius: 12px;
    color: white;
    font-size: 11px;
    font-weight: 500;
    min-width: 50px;
}

.stat-badge.pending {
    background: linear-gradient(135deg, #ff9800, #f57c00);
    box-shadow: 0 2px 4px rgba(255, 152, 0, 0.3);
}

.stat-badge.approved {
    background: linear-gradient(135deg, #4caf50, #388e3c);
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
}

.stat-badge.rejected {
    background: linear-gradient(135deg, #f44336, #d32f2f);
    box-shadow: 0 2px 4px rgba(244, 67, 54, 0.3);
}

.stat-badge.no-data {
    background: linear-gradient(135deg, #9e9e9e, #757575);
    box-shadow: 0 2px 4px rgba(158, 158, 158, 0.3);
}

.stat-badge .stat-number {
    font-size: 14px;
    font-weight: bold;
    line-height: 1;
}

.stat-badge .stat-label {
    font-size: 9px;
    opacity: 0.9;
    line-height: 1;
}

.employee-action {
    margin-top: 8px;
}

.modern-btn {
    border-radius: 20px;
    padding: 6px 16px;
    font-size: 12px;
    font-weight: 500;
    border: none;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.modern-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* History Tab Improvements */
.history-item {
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.history-item:hover {
    transform: translateX(5px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Pending Tab Improvements */
.panel-info {
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    border-left: 4px solid #17a2b8;
}

.panel-info:hover {
    transform: translateX(3px);
    box-shadow: 0 2px 8px rgba(23, 162, 184, 0.2);
}

.manager-approval-form {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.manager-approval-form .btn {
    border-radius: 20px;
    padding: 8px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.manager-approval-form .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* Filter Section Improvements */
.filter-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    padding: 20px;
    border-radius: 12px;
    border: 1px solid #e9ecef;
    margin-bottom: 20px;
}

/* Responsive */
@media (max-width: 768px) {
    .employee-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
        padding: 16px;
    }

    .employee-info {
        width: 100%;
    }

    .employee-stats {
        width: 100%;
        justify-content: space-between;
    }

    .employee-meta {
        flex-direction: column;
        gap: 8px;
    }

    .avatar-circle {
        width: 40px;
        height: 40px;
        font-size: 14px;
    }

    .employee-name {
        font-size: 16px;
    }

    .all-employee-stats {
        justify-content: flex-start;
    }

    .stat-badge {
        min-width: 45px;
        padding: 3px 6px;
    }
}

@media (max-width: 480px) {
    .employee-header {
        padding: 12px;
    }

    .history-stats {
        flex-direction: column;
        gap: 8px;
    }

    .stat-item {
        padding: 4px 8px;
    }

    .pending-count {
        padding: 6px 12px;
    }

    .count-number {
        font-size: 18px;
    }

    .all-employee-stats {
        flex-direction: column;
        gap: 6px;
    }

    .stat-badge {
        flex-direction: row;
        gap: 8px;
        justify-content: space-between;
    }
}

/* Bulk Actions Styles */
.bulk-actions-panel {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.bulk-info {
    font-size: 16px;
    font-weight: 600;
    color: #856404;
}

.bulk-info i {
    color: #f39c12;
    margin-right: 8px;
}

.checkbox-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.explanation-checkbox {
    display: none;
}

.checkbox-label {
    cursor: pointer;
    font-size: 18px;
    color: #95a5a6;
    transition: all 0.3s ease;
    margin: 0;
}

.checkbox-label .checked {
    display: none;
    color: #27ae60;
}

.checkbox-label .unchecked {
    display: inline;
}

.explanation-checkbox:checked + .checkbox-label .checked {
    display: inline;
}

.explanation-checkbox:checked + .checkbox-label .unchecked {
    display: none;
}

.checkbox-label:hover {
    color: #3498db;
    transform: scale(1.1);
}

.explanation-checkbox:checked + .checkbox-label:hover {
    color: #27ae60;
}

/* Selected explanation highlight */
.explanation-selected {
    background-color: #e8f5e9 !important;
    border-left: 4px solid #4caf50 !important;
}

.explanation-selected .panel-heading {
    background-color: #c8e6c9 !important;
}
</style>

<!-- Modal xác nhận bulk actions -->
<div class="modal fade" id="bulkActionModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">
                    <i class="fa fa-check-square-o"></i> Xác nhận duyệt hàng loạt
                </h4>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fa fa-info-circle"></i>
                    Bạn đang thực hiện <strong id="bulkActionType"></strong> cho <strong id="bulkActionCount"></strong> giải trình đã chọn.
                </div>

                <div id="bulkActionList" class="list-group" style="max-height: 300px; overflow-y: auto;">
                    <!-- Danh sách giải trình sẽ được load bằng JavaScript -->
                </div>

                <div class="form-group">
                    <label for="bulkNote">Ghi chú chung (tùy chọn):</label>
                    <textarea id="bulkNote" class="form-control" rows="3" maxlength="1000" placeholder="Ghi chú áp dụng cho tất cả giải trình được chọn..."></textarea>
                    <small class="text-muted">Tối đa 1000 ký tự</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">
                    <i class="fa fa-times"></i> Hủy
                </button>
                <button type="button" class="btn btn-primary" id="confirmBulkAction">
                    <i class="fa fa-check"></i> Xác nhận thực hiện
                </button>
            </div>
        </div>
    </div>
</div>
@endsection
